# 实现计划

## 智能错误解析器完整操作流程

- [ ] 1.  运行 inspect_method.py 生成方法问题报告
          - **工具执行**: 使用 `java_error_analyzer\inspect_method.py` 脚本
          - **文件路径**：使用当前项目
          - **数据来源**: 使用现有的 `java_error_analyzer\ai_method.xml` 文件
          - **输出目录**: `output` 目录
          - **重要提示**: **必须使用绝对路径**，相对路径会导致 IntelliJ IDEA 无法正确识别项目和生成 JavaAnnotator.xml 文件
          - **推荐执行命令示例**:

            ```bash
            # 使用绝对路径（推荐，已验证成功）
            python java_error_analyzer\inspect_method.py --project-path "E:\work\project\energy-solution-fusion-demo\eem-solution-group-energy" --inspection-profile "E:\work\project\energy-solution-fusion-demo\eem-solution-group-energy\java_error_analyzer\ai_method.xml" --output-path "output"

            # 使用相对路径（不推荐，会导致 JavaAnnotator.xml 文件生成失败）
            python java_error_analyzer\inspect_method.py --project-path "." --inspection-profile "java_error_analyzer\ai_method.xml" --output-path "output"
            ```

          - **参数说明**:
            - `--project-path`: 要检查的 Java 项目根目录（**强烈建议使用绝对路径**）
            - `--inspection-profile`: 检查配置文件的完整路径（**强烈建议使用绝对路径**）
            - `--output-path`: 输出目录路径
          - **路径问题说明**:
            - IntelliJ IDEA 的 inspect 命令对路径处理严格，相对路径可能导致项目识别失败
            - 使用绝对路径确保工具能准确定位项目根目录和配置文件
            - 相对路径虽然预检查通过，但会导致 JavaAnnotator.xml 文件无法生成
          - **输出文件**: `output\method_issues_report.json`
          - **执行结果**:
            - 成功生成 JavaAnnotator.xml 文件（9900 字节）
            - 解析到 9 个问题（8个 wrong_params，1个 miss_method）
            - 涉及 TeamEnergyServiceImpl（8个问题）和 TeamConfigServiceImpl（1个问题）
          - **报告格式**:
            ```json
            [
          {
          "issue_id": 107,
          "error_code": "method_issues",
          "module": "eem-solution-group-energy-core",
          "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
          "class": "TeamEnergyServiceImpl",
          "missing_method": "getProjectTree()",
          "description": "无法解析方法 'getProjectTree(Integer)'",
          "line": "466"

      }
      ]

      ```

          - **执行说明**:
            - 如果 `inspect.bat` 不可用，脚本会直接解析现有的 `JavaAnnotator.xml` 文件
            - 脚本会筛选出所有"无法解析方法"相关的问题
            - 按类名分组生成结构化的方法问题报告
          - _输出: 完整的 output\method_issues_report.json  文件，包含所有方法解析问题的详细信息_
      ```

- [ ] 2. 逐个错误处理主流程

  - [ ] 2.1 读取单个错误信息（大模型直接执行）

        - **数据读取**：大模型直接读取 output\method_issues_report.json 数据源
        - **逐个错误分析**：大模型逐个读取和分析每个错误记录，不使用脚本
        - **错误信息解析**：大模型解析错误的基本信息（文件路径、行号、错误消息、类名、方法名）
        - **唯一标识符分配**：为每个错误分配或保持唯一标识符
        - **AI智能分析**：大模型基于错误描述和上下文进行智能分析
        - **错误类型判断**：大模型判断错误类型：miss_method、wrong_params 或 unidentified
        - **分类依据记录**：大模型记录分类结果和详细的分析依据
        - **严格要求**：
          - 必须由大模型直接执行，不允许编写自动化脚本
          - 每个错误都要进行人工智能分析，提供分析过程和依据
          - 分类结果必须基于AI理解，而非简单的规则匹配
        - **输出格式要求**：
        ```json
        [
          {
            "issue_id": 107,
            "error_code": "method_issues",
            "module": "eem-solution-group-energy-core",
            "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
            "class": "TeamEnergyServiceImpl",
            "missing_method": "getProjectTree()",
            "description": "无法解析方法 'getProjectTree(Integer)'",
            "line": "466",
            "ai_analysis": "大模型的详细分析过程和结论",
            "classification_reason": "大模型的分类依据说明"
          }
        ]
        ```
        - **输出文件**：分别输出到 output\miss_method.json、output\wrong_params.json、output\unidentified.json
        - _需求: 1.1, 6.1, 6.2_

    ```

    ```

- [ ] 3. miss_method 错误专项处理流程

  - [ ] 3.1 缺失方法源码信息获取

    - 调用 source-context-extractor 处理 output\miss_method.json
    - 执行命令: `python -m source_context_extractor.main -i output/miss_method.json -o output/miss_method_report.md --src-path "<项目源码路径>"`
    - **参数说明**:
      - `--src-path`: 指定项目的源码根目录，通常是包含 src/main/java 的模块路径
      - 示例: `"eem-solution-group-energy-core/src"` 或 `"your-project-module/src"`
    - 输出到 output\miss_method_report.md
    - _需求: 2.1, 2.2, 2.4_

  - [ ] 3.2 知识库解决方案搜索

    - **大模型直接执行**：不创建脚本，由大模型直接读取和处理
    - 读取 output\miss_method_report.md，一个问题一个问题读取然后处理
    - **优先级 1：SDK 方法直接替换**
      - 在知识库中搜索 SDK 提供的直接替换方法
      - 查找功能相同或相似的 SDK 方法
      - 提供完整的导入语句和方法调用示例
      - 确保参数类型和返回值兼容性
    - **优先级 2：SDK 方法重构方案**
      - 如果无法直接替换，分析原方法的业务逻辑
      - 使用多个 SDK 方法组合实现相同功能
      - 提供重构后的完整代码实现
      - 保持原有方法签名和返回值不变
    - **优先级 3：废弃 API 迁移指导**
      - 查找废弃 API 的替代方案和迁移指导
      - 匹配类名、方法名的替换映射关系
      - 提取具体的导入语句和代码修改示例
    - **优先级 4：配置和依赖修复**
      - 确定是导入问题还是方法真正缺失
      - 如果是导入问题：提供正确的 import 语句
      - 检查依赖注入配置（插件前缀等）
      - 验证常量类和多租户字段更新
    - **严格要求：不允许自创方法**
      - 所有解决方案必须基于知识库中明确存在的方法和类
      - 不允许自己发挥创造不存在的方法或 API
      - 如果知识库中无法找到可替换的方法，必须标记为 🔴 未识别
      - 宁可标记为未识别，也不能提供错误的解决方案
    - 保证不漏，方案正确，如果无法处理则分类到未识别
    - 基于源码信息和知识库结果生成具体修复方案
    - 生成详细的修复步骤和代码示例
    - 标记解决方案优先级（🟢 确定/🟡 需验证/🔴 未识别）
    - 输出到 output\miss_method_fix.md
    - _需求: 3.1, 3.2, 3.4，1.2, 4.1, 4.2, 4.3_

- [ ] 4. wrong_params 错误专项处理流程

  - [ ] 4.1 方法签名对比分析（大模型直接处理）

    - **数据读取**：逐个读取 output\wrong_params.json 中的参数不匹配错误
    - **方法签名获取**：大模型从错误描述中提取目标方法的正确签名信息
    - **源码查找**：大模型调用 java_error_analyzer\FindNameFromJarAndSource.py 定位相关类和方法
    - **差异对比分析**：
      - 对比参数类型、数量、顺序的具体差异
      - 分析返回类型的变化情况
      - 识别参数类型不兼容的根本原因
    - **修复方案生成**：
      - 生成参数类型转换的具体建议
      - 提供参数顺序调整的完整方案
      - 处理新增或删除参数的适配策略
      - 生成可直接使用的方法调用修改代码
      - 提供参数转换的完整示例代码
    - **输出**：将完整分析和修复方案输出到 output\wrong_params_fix.md
    - _需求: 2.1, 2.2, 4.1_

- [ ] 5. unidentified 错误处理流程

  - [ ] 5.1 未识别错误信息收集
    - 读取 output\unidentified.json 问题清单，逐个问题读取
    - 保存错误上下文和相关代码片段
    - 记录分析失败的具体原因
    - 将错误标记为需要人工处理
    - 提供尽可能多的分析上下文信息
    - 建议可能的处理方向或工具
    - 输出方案到 output\unidentified_fix.md
    - _需求: 1.4, 5.3_

- [ ] 6. 质量检查和完整性验证

  - [ ] 6.1 问题处理完整性检查

    - **数据源验证**：统计 output\method_issues_report.json 中的总问题数量
    - **分类完整性检查**：
      - 验证 miss_method.json + wrong_params.json + unidentified.json 的问题总数
      - 确保所有问题都已分类，无遗漏
      - 检查是否存在重复分类的问题
    - **处理状态验证**：
      - 确认每个 miss_method 问题都在 miss_method_fix.md 中有对应方案
      - 确认每个 wrong_params 问题都在 wrong_params_fix.md 中有对应方案
      - 确认每个 unidentified 问题都在 unidentified_fix.md 中有记录
    - **遗漏问题识别**：
      - 列出所有未处理的问题 ID
      - 分析遗漏原因并补充处理
    - _需求: 6.1, 6.2_

  - [ ] 6.2 解决方案合规性检查

        - **miss_method 方案合规检查**：
          - 验证所有解决方案都基于知识库中存在的方法和类
          - 检查是否存在自创的不存在的方法或 API
          - 确认导入语句的正确性和完整性
          - 验证参数类型和返回值的兼容性
          - 检查优先级标记的准确性（🟢 确定/🟡 需验证/🔴 未识别）
        - **wrong_params 方案合规检查**：
          - 验证参数类型转换的正确性
          - 检查方法签名对比的准确性
          - 确认修复代码的语法正确性
          - 验证类型转换不会导致数据丢失或异常
        - **方案可执行性验证**：
          - 检查所有修复方案是否提供了完整的实现代码
          - 验证导入语句和依赖的可用性
          - 确认修复方案不会引入新的编译错误
        - **不合规问题处理**：
          - 标记不合规的解决方案
          - 重新分析并提供合规的替代方案
          - 如无法提供合规方案，重新分类为未识别
        - _需求: 3.1, 3.2, 4.1, 4.2_



- [ ] 7. 代码修复执行流程（大模型直接修复）

  - [ ] 7.1 miss_method 问题代码修复

    - **修复方案读取**：读取 output\miss_method_fix.md 中的所有修复方案
    - **逐个问题处理**：
      - 按问题 ID 顺序，一个问题一个问题进行修复
      - 定位到具体的源文件和错误行号
      - 根据修复方案中的 🟢 确定方案直接应用修复
      - 对 🟡 需验证方案，先进行可行性验证后再修复
      - 🔴 未识别问题跳过，记录到修复日志
    - **代码修复操作**：
      - 添加缺失的 import 语句到文件顶部
      - 替换错误的方法调用为正确的 SDK 方法
      - 修改方法参数和返回值类型
      - 更新依赖注入配置和常量引用
    - **修复验证**：
      - 检查修复后的代码语法正确性
      - 验证 import 语句无冲突
      - 确保修复不影响其他代码逻辑
    - **修复记录**：记录每个问题的修复状态和结果
    - _需求: 3.1, 3.2, 4.1, 4.2_

  - [ ] 7.2 wrong_params 问题代码修复

    - **修复方案读取**：读取 output\wrong_params_fix.md 中的所有修复方案
    - **逐个问题处理**：
      - 按问题 ID 顺序，一个问题一个问题进行修复
      - 定位到具体的源文件和错误行号
      - 根据参数类型转换方案修改方法调用
    - **代码修复操作**：
      - 修改方法调用的参数类型和顺序
      - 添加必要的类型转换代码
      - 构造正确的 DTO 对象替换错误参数
      - 处理返回值类型不匹配问题
    - **修复验证**：
      - 验证参数类型转换的正确性
      - 检查方法调用语法的准确性
      - 确保类型转换不会导致运行时异常
    - **修复记录**：记录每个问题的修复状态和结果
    - _需求: 2.1, 2.2, 4.1_

  - [ ] 7.3 修复结果汇总
    - **修复统计**：
      - 统计成功修复的问题数量
      - 记录修复失败的问题和原因
      - 计算各类型问题的修复成功率
    - **代码变更记录**：
      - 列出所有被修改的文件清单
      - 记录每个文件的具体变更内容
      - 提供修复前后的代码对比
    - **风险评估**：
      - 识别可能引入新问题的修复
      - 标记需要额外测试验证的修改
      - 提供回滚方案建议
    - **输出**：生成 output\code_fix_report.md 修复执行报告
    - _需求: 6.1, 6.2_

- [ ] 9. 修复验证和测试

  - [ ] 9.1 编译验证

    - 确保所有修复后的代码能够正常编译
    - 解决修复过程中引入的新问题
    - 验证依赖关系的正确性
    - 生成最终的编译报告
    - _目标: 实现零编译错误_

  - [ ] 9.2 功能测试验证
    - 对修复的关键功能进行测试
    - 验证业务逻辑的正确性
    - 检查性能和稳定性影响
    - 记录测试结果和问题
    - _目标: 确保功能正常_

- [ ] 10. 文档和总结

  - [ ] 10.1 生成修复报告

    - 统计修复的问题数量和类型
    - 记录修复过程中的关键决策
    - 整理遗留问题和风险点
    - 生成完整的修复报告
    - _输出: 迁移修复总结报告_

  - [ ] 10.2 更新项目文档
    - 更新项目的技术文档
    - 记录重要的架构变更
    - 更新部署和运维指南
    - 提供后续维护建议
    - _输出: 更新的项目文档_
