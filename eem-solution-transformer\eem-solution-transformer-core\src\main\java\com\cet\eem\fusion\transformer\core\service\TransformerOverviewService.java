package com.cet.eem.fusion.transformer.core.service;

import com.cet.eem.fusion.transformer.core.entity.dto.EquipmentCondition;
import com.cet.eem.fusion.transformer.core.entity.dto.EquipmentConditionDTO;
import com.cet.eem.fusion.transformer.core.entity.dto.EquipmentForm;
import com.cet.eem.fusion.transformer.core.entity.dto.EquipmentFormDTO;
import com.cet.eem.fusion.transformer.core.entity.vo.OverviewDataVO;
import com.cet.eem.fusion.transformer.core.entity.vo.OverviewDataVo;

import java.util.List;

public interface TransformerOverviewService {
    /**
     * 变压器总览 查询上面固定的数据
     * @param projectId
     * @return
     */
    OverviewDataVo aboveData(Long projectId) ;

    /**
     * 变压器总览-日事件统计
     * @param projectId
     * @return
     */
    OverviewDataVo aboveDataForDayEvent(Long projectId) ;

    /**
     * 变压器总览-月事件统计
     * @param projectId
     * @return
     */
    OverviewDataVo aboveDataForMonthEvent(Long projectId) ;

    /**
     * 变压器总览-状态统计
     * @param projectId
     * @return
     */
    OverviewDataVo aboveDataForStatus(Long projectId) ;

    /**
     * 设备状态信息
     * @param form
     * @param projectId
     * @return
     */
    List<EquipmentCondition> equipmentStatus(EquipmentForm form,Long projectId) ;

}
