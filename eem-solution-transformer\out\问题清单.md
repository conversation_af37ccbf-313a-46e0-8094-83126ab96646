# 代码扫描问题报告

总问题数: 175

## DateUtil

### 问题 1
error_type: "类问题"
error_code: "DateUtil_issues"
calling_class: "DateUtil"
usage_pattern: "DateUtil"
suggest: "请使用TimeUtil重构"
line: ["17"]

## LoadRateVo

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "TimeValue"
calling_class: "LoadRateVo"
old_dependency: "com.cet.eem.fusion.transformer.core.entity.vo"
current_dependency: "eem-solution-transformer-core"
line: ["[18]"]

## OverviewDataVo

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "Quantity"
calling_class: "OverviewDataVo"
old_dependency: "com.cet.eem.fusion.transformer.core.entity.vo"
current_dependency: "eem-solution-transformer-core"
line: ["[16]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "Operation"
calling_class: "OverviewDataVo"
old_dependency: "com.cet.eem.fusion.transformer.core.entity.vo"
current_dependency: "eem-solution-transformer-core"
line: ["[14]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "Event"
calling_class: "OverviewDataVo"
old_dependency: "com.cet.eem.fusion.transformer.core.entity.vo"
current_dependency: "eem-solution-transformer-core"
line: ["[18]"]

## PowerTransformerDaoImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "ProjectDto"
calling_class: "PowerTransformerDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[43]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "NodeLabelDef"
calling_class: "PowerTransformerDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[29, 35, 43]"]

### 问题 3
error_type: "类问题"
error_code: "regex_pattern"
calling_class: "PowerTransformerDaoImpl"
calling_method: "modelServiceUtils_queryWithChildren"
suggest: "请使用ParentQueryConditionBuilder.leftJoinSubBuilder替代modelServiceUtils.queryWithChildren泛型"
line: ["35"]

### 问题 4
error_type: "类问题"
error_code: "regex_pattern"
calling_class: "PowerTransformerDaoImpl"
calling_method: "modelServiceUtils_queryWithChildren"
suggest: "请使用ParentQueryConditionBuilder.leftJoinSubBuilder替代modelServiceUtils.queryWithChildren泛型"
line: ["43"]

## PowerTransformerDto

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerVo"
calling_class: "PowerTransformerDto"
old_dependency: "com.cet.eem.fusion.transformer.core.entity.dto"
current_dependency: "eem-solution-transformer-core"
line: ["[19]"]

## ProjectDto

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "Project"
calling_class: "ProjectDto"
old_dependency: "com.cet.eem.fusion.transformer.core.entity.dto"
current_dependency: "eem-solution-transformer-core"
line: ["[16]"]

## TransformerAnalysisController

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadRateVo"
calling_class: "TransformerAnalysisController"
old_dependency: "com.cet.eem.fusion.transformer.core.controller"
current_dependency: "eem-solution-transformer-core"
line: ["[62]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadRateParam"
calling_class: "TransformerAnalysisController"
old_dependency: "com.cet.eem.fusion.transformer.core.controller"
current_dependency: "eem-solution-transformer-core"
line: ["[62]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "Result"
calling_class: "TransformerAnalysisController"
old_dependency: "com.cet.eem.fusion.transformer.core.controller"
current_dependency: "eem-solution-transformer-core"
line: ["[39, 45, 51, 57, 63]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "VoltageSideMonitorVo"
calling_class: "TransformerAnalysisController"
old_dependency: "com.cet.eem.fusion.transformer.core.controller"
current_dependency: "eem-solution-transformer-core"
line: ["[44]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadInfoVo"
calling_class: "TransformerAnalysisController"
old_dependency: "com.cet.eem.fusion.transformer.core.controller"
current_dependency: "eem-solution-transformer-core"
line: ["[50]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentMonitorVo"
calling_class: "TransformerAnalysisController"
old_dependency: "com.cet.eem.fusion.transformer.core.controller"
current_dependency: "eem-solution-transformer-core"
line: ["[38]"]

### 问题 7
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TransformerAnalysisController"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["30"]

### 问题 8
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TransformerAnalysisController"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["33"]

## TransformerAnalysisService

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "DataLogData"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[76, 84]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerDto"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[84, 92]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityDataBatchSearchVo"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[76, 84]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadInfoVo"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[37]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "VoltageSideMonitorVo"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[29]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentMonitorVo"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[19]"]

### 问题 7
error_type: "类问题"
error_code: "类问题"
missing_class: "PointNode"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[65, 76, 101, 110]"]

### 问题 8
error_type: "类问题"
error_code: "类问题"
missing_class: "LinkNode"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[65, 76, 101, 110]"]

### 问题 9
error_type: "类问题"
error_code: "类问题"
missing_class: "BaseVo"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[65]"]

### 问题 10
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadRateVo"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[55]"]

### 问题 11
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadRateParam"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[55]"]

### 问题 12
error_type: "类问题"
error_code: "类问题"
missing_class: "RadarChartInfo"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[46]"]

### 问题 13
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisService"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["76"]

### 问题 14
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisService"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["84"]

## TransformerAnalysisServiceImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "RealTimeValue"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[838, 937, 939, 940, 941, 942, 943, 944, 945, 965, 971, 1022, 1032, 1041, 1043, 1044, 1053, 1055, 1056, 1065, 1067, 1068, 1276, 1289, 1298]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerDto"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[98, 123, 262, 355, 361, 362, 411, 413, 425, 426, 445, 459, 462, 472, 510, 1217, 1219, 1233]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityDataBatchSearchVo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[291, 335, 356, 412, 446, 460, 512, 698, 809, 810, 1207, 1208]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "EnergyTypeDef"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[113, 127, 165, 271, 1084, 1093, 1102, 1112, 1121, 1130, 1139, 1148, 1157, 1167, 1176, 1185, 1194]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "TimeValue"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[761, 763, 765]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "VoltageSideMonitorVo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[112, 116, 117, 955, 971, 972, 1011]"]

### 问题 7
error_type: "类问题"
error_code: "类问题"
missing_class: "NodeDao"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[83]"]

### 问题 8
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentMonitorVo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[97, 100, 102, 1029]"]

### 问题 9
error_type: "类问题"
error_code: "类问题"
missing_class: "NumberCalcUtils"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[240, 241, 242, 245, 324, 604, 605, 606, 614, 734, 788, 824, 826, 1252, 1254, 1307, 1315]"]

### 问题 10
error_type: "类问题"
error_code: "类问题"
missing_class: "EnumOperationType"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[240, 241, 242, 245, 324, 604, 605, 606, 616, 735, 788, 824, 826, 1253, 1254, 1308, 1315]"]

### 问题 11
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadInfoVo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[122, 125, 132, 834, 937, 938, 1263]"]

### 问题 12
error_type: "类问题"
error_code: "类问题"
missing_class: "EnumDataTypeId"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[811, 1209]"]

### 问题 13
error_type: "类问题"
error_code: "类问题"
missing_class: "AggregationType"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[183, 306, 344]"]

### 问题 14
error_type: "类问题"
error_code: "类问题"
missing_class: "RadarChartInfo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[160, 163, 199, 782]"]

### 问题 15
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerVo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[161]"]

### 问题 16
error_type: "类问题"
error_code: "类问题"
missing_class: "Topology1Service"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[75]"]

### 问题 17
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityObjectDao"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[77]"]

### 问题 18
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadRateVo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[261, 263]"]

### 问题 19
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadRateParam"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[261]"]

### 问题 20
error_type: "类问题"
error_code: "类问题"
missing_class: "SectionVo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[144, 268, 820, 825, 827, 828, 829]"]

### 问题 21
error_type: "类问题"
error_code: "类问题"
missing_class: "PipeNetworkConnectionModelDao"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[81]"]

### 问题 22
error_type: "类问题"
error_code: "类问题"
missing_class: "QueryConditionBuilder"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[755]"]

### 问题 23
error_type: "类问题"
error_code: "类问题"
missing_class: "BaseEntity"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[755]"]

### 问题 24
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityManageService"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[73]"]

### 问题 25
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["40"]

### 问题 26
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["291"]

### 问题 27
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["335"]

### 问题 28
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["356"]

### 问题 29
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["412"]

### 问题 30
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["446"]

### 问题 31
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["460"]

### 问题 32
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["512"]

### 问题 33
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["698"]

### 问题 34
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["809"]

### 问题 35
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["810"]

### 问题 36
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["810"]

### 问题 37
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["1207"]

### 问题 38
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["1208"]

### 问题 39
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["1208"]

### 问题 40
error_type: "类问题"
error_code: "QueryConditionBuilder_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QueryConditionBuilder"
suggest: "QueryConditionBuilder废弃，请使用ParentQueryConditionBuilder重构"
line: ["755"]

### 问题 41
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: "TransformerAnalysisServiceImpl -> QuantityObjectDao"
suggest: "请使用新的QuantityObjectService替代QuantityObjectDao"
line: ["{'声明': 77, '使用': [173, 297, 418, 467]}"]

### 问题 42
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: "TransformerAnalysisServiceImpl -> QuantityAggregationDataDao"
suggest: "请使用新的QuantityAggregationDataService替代QuantityAggregationDataDao"
line: ["{'声明': 79, '使用': [181, 305, 339]}"]

### 问题 43
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: "TransformerAnalysisServiceImpl -> QuantityManageService"
suggest: "QuantityManageService已经废弃"
line: ["{'声明': 73, '使用': [699, 838, 965, 1022, 1032, 1276]}"]

### 问题 44
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: "TransformerAnalysisServiceImpl -> Topology1Service"
suggest: "Topology1Service已经废弃（平台提供了PipeNetworkConnectionServiceImpl需要重构）"
line: ["{'声明': 75, '使用': [113, 127, 165, 271]}"]

### 问题 45
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: "TransformerAnalysisServiceImpl -> PipeNetworkConnectionModelDao"
suggest: "PipeNetworkConnectionModelDao已经废弃（平台提供了PipeNetworkConnectionServiceImpl需要重构）"
line: ["{'声明': 81, '使用': [415, 464]}"]

### 问题 46
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: "TransformerAnalysisServiceImpl -> NodeDao"
suggest: "NodeDao已经废弃，考虑通过EemNodeService重构"
line: ["{'声明': 83}"]

## TransformerOverviewController

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "Result"
calling_class: "TransformerOverviewController"
old_dependency: "com.cet.eem.fusion.transformer.core.controller"
current_dependency: "eem-solution-transformer-core"
line: ["[35, 41, 47, 53, 59]"]

### 问题 2
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TransformerOverviewController"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["29"]

## TransformerOverviewService

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentCondition"
calling_class: "TransformerOverviewService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[44]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentForm"
calling_class: "TransformerOverviewService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[44]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "OverviewDataVo"
calling_class: "TransformerOverviewService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[15, 22, 29, 36]"]

## TransformerOverviewServiceImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "OverviewDataVo"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[99, 101, 125, 128, 138, 140, 150, 153]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerDto"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[102, 129, 141, 154, 168, 193, 194, 209, 240, 259, 364, 370, 373, 433, 457, 466, 469, 501, 508, 540, 542, 570, 601, 622, 642]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentCondition"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[166, 197, 259, 287, 364, 372, 394]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentForm"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[166, 259, 314, 364]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentStatus"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[170]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "LOADRATE"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[174]"]

### 问题 7
error_type: "类问题"
error_code: "类问题"
missing_class: "Constant"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[176, 179, 182, 185, 188, 213, 215, 220, 221, 222, 225, 226, 227, 230, 231, 232, 247, 395, 434, 504, 505, 506, 507, 550, 552, 733, 734, 735, 736, 737, 743, 744, 745, 746, 747]"]

### 问题 8
error_type: "类问题"
error_code: "类问题"
missing_class: "AVERAGEPOWERFACTOR"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[178]"]

### 问题 9
error_type: "类问题"
error_code: "类问题"
missing_class: "OPERATIONRATE"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[181]"]

### 问题 10
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerindexData"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[195]"]

### 问题 11
error_type: "类问题"
error_code: "类问题"
missing_class: "BaseVo"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[210, 240, 243, 248, 265, 266, 267, 269, 270, 273, 335, 580, 608, 628, 642, 643, 653, 671, 688]"]

### 问题 12
error_type: "类问题"
error_code: "类问题"
missing_class: "ConnectionSearchVo"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[241]"]

### 问题 13
error_type: "类问题"
error_code: "类问题"
missing_class: "Operation"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[105, 157, 457, 458]"]

### 问题 14
error_type: "类问题"
error_code: "类问题"
missing_class: "Event"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[540, 541, 570, 571, 601, 602, 622, 623]"]

### 问题 15
error_type: "类问题"
error_code: "类问题"
missing_class: "PecEventCountVo"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[672]"]

### 问题 16
error_type: "类问题"
error_code: "类问题"
missing_class: "AggregationResult"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[659, 660, 678, 679]"]

### 问题 17
error_type: "类问题"
error_code: "类问题"
missing_class: "PecEventExtendVo"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[552, 557, 742, 749]"]

### 问题 18
error_type: "类问题"
error_code: "类问题"
missing_class: "SystemEventCountVo"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[654]"]

### 问题 19
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerDao"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[76]"]

### 问题 20
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerOverviewService"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[72]"]

### 问题 21
error_type: "类问题"
error_code: "类问题"
missing_class: "Topology1Service"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[80]"]

### 问题 22
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityDataBatchSearchVo"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[365]"]

### 问题 23
error_type: "类问题"
error_code: "类问题"
missing_class: "NumberCalcUtils"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[380, 471]"]

### 问题 24
error_type: "类问题"
error_code: "类问题"
missing_class: "TopologyCommonService"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[92]"]

### 问题 25
error_type: "类问题"
error_code: "类问题"
missing_class: "Quantity"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[501, 502]"]

### 问题 26
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerlevelEnum"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[509]"]

### 问题 27
error_type: "类问题"
error_code: "类问题"
missing_class: "LOWTRANSFORMER"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[515]"]

### 问题 28
error_type: "类问题"
error_code: "类问题"
missing_class: "MIDTRANSFORMER"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[518]"]

### 问题 29
error_type: "类问题"
error_code: "类问题"
missing_class: "HIGHTRANSFORMER"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[521]"]

### 问题 30
error_type: "类问题"
error_code: "类问题"
missing_class: "PecEventService"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[94]"]

### 问题 31
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityObjectDao"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[84]"]

### 问题 32
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerindexDataDao"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[90]"]

### 问题 33
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityManageService"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[88]"]

### 问题 34
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["45"]

### 问题 35
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["365"]

### 问题 36
error_type: "类问题"
error_code: "PecEventExtendVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventExtendVo"
line: ["11"]

### 问题 37
error_type: "类问题"
error_code: "PecEventExtendVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventExtendVo"
line: ["552"]

### 问题 38
error_type: "类问题"
error_code: "PecEventExtendVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventExtendVo"
line: ["557"]

### 问题 39
error_type: "类问题"
error_code: "PecEventExtendVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventExtendVo"
line: ["742"]

### 问题 40
error_type: "类问题"
error_code: "PecEventExtendVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventExtendVo"
line: ["749"]

### 问题 41
error_type: "类问题"
error_code: "ConnectionSearchVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "ConnectionSearchVo"
line: ["13"]

### 问题 42
error_type: "类问题"
error_code: "ConnectionSearchVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "ConnectionSearchVo"
line: ["241"]

### 问题 43
error_type: "类问题"
error_code: "ConnectionSearchVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "ConnectionSearchVo"
line: ["241"]

### 问题 44
error_type: "类问题"
error_code: "PecEventCountVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventCountVo"
line: ["33"]

### 问题 45
error_type: "类问题"
error_code: "PecEventCountVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventCountVo"
line: ["672"]

### 问题 46
error_type: "类问题"
error_code: "PecEventCountVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventCountVo"
line: ["672"]

### 问题 47
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerOverviewServiceImpl"
calling_method: "TransformerOverviewServiceImpl -> QuantityObjectDao"
suggest: "请使用新的QuantityObjectService替代QuantityObjectDao"
line: ["{'声明': 84, '使用': [280]}"]

### 问题 48
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerOverviewServiceImpl"
calling_method: "TransformerOverviewServiceImpl -> QuantityAggregationDataDao"
suggest: "请使用新的QuantityAggregationDataService替代QuantityAggregationDataDao"
line: ["{'声明': 86, '使用': [317]}"]

### 问题 49
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerOverviewServiceImpl"
calling_method: "TransformerOverviewServiceImpl -> QuantityManageService"
suggest: "QuantityManageService已经废弃"
line: ["{'声明': 88, '使用': [691]}"]

### 问题 50
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerOverviewServiceImpl"
calling_method: "TransformerOverviewServiceImpl -> Topology1Service"
suggest: "Topology1Service已经废弃（平台提供了PipeNetworkConnectionServiceImpl需要重构）"
line: ["{'声明': 80, '使用': [442]}"]

### 问题 51
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerOverviewServiceImpl"
calling_method: "TransformerOverviewServiceImpl -> PecEventService"
suggest: "PecEventService已经废弃（需要重构）"
line: ["{'声明': 94, '使用': [678]}"]

### 问题 52
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerOverviewServiceImpl"
calling_method: "TransformerOverviewServiceImpl -> TopologyCommonService"
suggest: "TopologyCommonService已经废弃"
line: ["{'声明': 92, '使用': [246]}"]

## TransformerTaskServiceImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "HistoricalLoadVo"
calling_class: "TransformerTaskServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[65, 74, 76, 92, 94, 100, 101, 111, 120, 147, 152, 167]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerDto"
calling_class: "TransformerTaskServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[73, 100, 104, 116, 147]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "Topology1Service"
calling_class: "TransformerTaskServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[52]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityManageService"
calling_class: "TransformerTaskServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[50]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "NumberCalcUtils"
calling_class: "TransformerTaskServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[156, 171]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityDataBatchSearchVo"
calling_class: "TransformerTaskServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[102, 132, 133]"]

### 问题 7
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerTaskServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["21"]

### 问题 8
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerTaskServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["102"]

### 问题 9
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerTaskServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["132"]

### 问题 10
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerTaskServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["133"]

### 问题 11
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerTaskServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["133"]

### 问题 12
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerTaskServiceImpl"
calling_method: "TransformerTaskServiceImpl -> QuantityManageService"
suggest: "QuantityManageService已经废弃"
line: ["{'声明': 50}"]

### 问题 13
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerTaskServiceImpl"
calling_method: "TransformerTaskServiceImpl -> Topology1Service"
suggest: "Topology1Service已经废弃（平台提供了PipeNetworkConnectionServiceImpl需要重构）"
line: ["{'声明': 52}"]

## TransformerindexData

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "EntityWithName"
calling_class: "TransformerindexData"
old_dependency: "com.cet.eem.fusion.transformer.core.entity.po"
current_dependency: "eem-solution-transformer-core"
line: ["[11]"]

## TransformerindexDataDaoImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "EemPoiRecord"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[59, 60]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "QueryCondition"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[31, 39, 50]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "ParentQueryConditionBuilder"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[31, 39, 50]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "Constant"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[31, 32, 39, 40, 41, 42, 43, 50, 51, 52, 53]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "ModelServiceUtils"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[22]"]

## TransformerindexDataServiceImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerDto"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[100, 111, 123, 142, 162, 176, 252, 257, 380, 457, 460, 468, 540]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerindexData"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[184, 190, 192, 194, 195, 197, 204, 207, 209, 212, 232, 239, 277, 278, 380, 457, 458, 470, 471, 540, 567, 575, 583, 656]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityObjectDao"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[75]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerindexDataDao"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[71]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "Constant"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[184, 191, 194, 196, 381, 460, 472, 583]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "Topology1Service"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[81]"]

### 问题 7
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityDataBatchSearchVo"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[465]"]

### 问题 8
error_type: "类问题"
error_code: "类问题"
missing_class: "NumberCalcUtils"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[471]"]

### 问题 9
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerAnalysisServiceImpl"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[73, 542, 543]"]

### 问题 10
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerOverviewServiceImpl"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[67]"]

### 问题 11
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerDao"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[69]"]

### 问题 12
error_type: "类问题"
error_code: "类问题"
missing_class: "DateUtil"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[318, 638]"]

### 问题 13
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerindexDataServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["30"]

### 问题 14
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerindexDataServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["465"]

### 问题 15
error_type: "类问题"
error_code: "DateUtil_issues"
calling_class: "TransformerindexDataServiceImpl"
usage_pattern: "DateUtil"
suggest: "请使用TimeUtil重构"
line: ["38"]

### 问题 16
error_type: "类问题"
error_code: "DateUtil_issues"
calling_class: "TransformerindexDataServiceImpl"
usage_pattern: "DateUtil"
suggest: "请使用TimeUtil重构"
line: ["318"]

### 问题 17
error_type: "类问题"
error_code: "DateUtil_issues"
calling_class: "TransformerindexDataServiceImpl"
usage_pattern: "DateUtil"
suggest: "请使用TimeUtil重构"
line: ["638"]

### 问题 18
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerindexDataServiceImpl"
calling_method: "TransformerindexDataServiceImpl -> QuantityObjectDao"
suggest: "请使用新的QuantityObjectService替代QuantityObjectDao"
line: ["{'声明': 75, '使用': [562]}"]

### 问题 19
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerindexDataServiceImpl"
calling_method: "TransformerindexDataServiceImpl -> QuantityAggregationDataDao"
suggest: "请使用新的QuantityAggregationDataService替代QuantityAggregationDataDao"
line: ["{'声明': 77, '使用': [615]}"]

### 问题 20
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerindexDataServiceImpl"
calling_method: "TransformerindexDataServiceImpl -> Topology1Service"
suggest: "Topology1Service已经废弃（平台提供了PipeNetworkConnectionServiceImpl需要重构）"
line: ["{'声明': 81, '使用': [593]}"]
